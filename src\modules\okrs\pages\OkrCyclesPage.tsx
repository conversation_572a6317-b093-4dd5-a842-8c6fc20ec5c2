import { format } from 'date-fns';
import React, { useMemo, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { ActiveFilters } from '@/modules/components/filters';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, DeleteConfirmModal, IconCard, Table, Tooltip } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';

import OkrCycleForm from '../components/OkrCycleForm';
import { useDeleteOkrCycle, useOkrCycles } from '../hooks/useOkrCycles';
import { OkrCycleQueryDto, OkrCycleResponseDto, OkrCycleStatus } from '../types/okr-cycle.types';

/**
 * Trang quản lý chu kỳ OKR
 */
const OkrCyclesPage: React.FC = () => {
  const { t } = useTranslation(['okrs', 'common']);
  const [cycleToDelete, setCycleToDelete] = useState<number | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [cycleToEdit, setCycleToEdit] = useState<OkrCycleResponseDto | undefined>(undefined);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Định nghĩa các tùy chọn bộ lọc
  const filterOptions = useMemo(() => {
    // Map status to translation keys
    const statusTranslationMap = {
      [OkrCycleStatus.ACTIVE]: 'okrs:cycle.status.active',
      [OkrCycleStatus.PLANNING]: 'okrs:cycle.status.planning',
      [OkrCycleStatus.CLOSED]: 'okrs:cycle.status.closed',
    };

    // Default fallback values for each status
    const fallbackMap = {
      [OkrCycleStatus.ACTIVE]: 'Active',
      [OkrCycleStatus.PLANNING]: 'Planning',
      [OkrCycleStatus.CLOSED]: 'Closed',
    };

    return [
      { id: 'all', label: t('common:all'), value: 'all' },
      {
        id: OkrCycleStatus.ACTIVE,
        label: t(statusTranslationMap[OkrCycleStatus.ACTIVE], fallbackMap[OkrCycleStatus.ACTIVE]),
        value: OkrCycleStatus.ACTIVE,
      },
      {
        id: OkrCycleStatus.PLANNING,
        label: t(
          statusTranslationMap[OkrCycleStatus.PLANNING],
          fallbackMap[OkrCycleStatus.PLANNING]
        ),
        value: OkrCycleStatus.PLANNING,
      },
      {
        id: OkrCycleStatus.CLOSED,
        label: t(statusTranslationMap[OkrCycleStatus.CLOSED], fallbackMap[OkrCycleStatus.CLOSED]),
        value: OkrCycleStatus.CLOSED,
      },
    ];
  }, [t]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<OkrCycleResponseDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'name',
        title: t('okrs:cycle.table.name', 'Tên chu kỳ'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
      },
      {
        key: 'startDate',
        title: t('okrs:cycle.table.startDate', 'Ngày bắt đầu'),
        dataIndex: 'startDate',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const date = value as string;
          return <div>{date ? format(new Date(date), 'dd/MM/yyyy') : ''}</div>;
        },
      },
      {
        key: 'endDate',
        title: t('okrs:cycle.table.endDate', 'Ngày kết thúc'),
        dataIndex: 'endDate',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const date = value as string;
          return <div>{date ? format(new Date(date), 'dd/MM/yyyy') : ''}</div>;
        },
      },
      {
        key: 'status',
        title: t('okrs:cycle.table.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as OkrCycleStatus;
          let className = '';

          // Map status to appropriate styling
          const statusStyleMap = {
            [OkrCycleStatus.ACTIVE]:
              'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
            [OkrCycleStatus.PLANNING]:
              'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
            [OkrCycleStatus.CLOSED]:
              'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
          };

          // Get the appropriate translation key for the status
          const statusTranslationMap = {
            [OkrCycleStatus.ACTIVE]: 'okrs:cycle.status.active',
            [OkrCycleStatus.PLANNING]: 'okrs:cycle.status.planning',
            [OkrCycleStatus.CLOSED]: 'okrs:cycle.status.closed',
          };

          className =
            statusStyleMap[status] ||
            'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
          const translationKey = statusTranslationMap[status];

          // Default fallback values for each status
          const fallbackMap = {
            [OkrCycleStatus.ACTIVE]: 'Active',
            [OkrCycleStatus.PLANNING]: 'Planning',
            [OkrCycleStatus.CLOSED]: 'Closed',
          };

          // Get translated label or fallback to status value or "Unknown"
          const label = translationKey
            ? t(translationKey, fallbackMap[status] || status)
            : status || t('common:unknown', 'Unknown');

          return (
            <div className={`px-2 py-1 rounded-full text-center text-xs font-medium ${className}`}>
              {label}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '15%',
        render: (_: unknown, record: OkrCycleResponseDto) => (
          <div className="flex space-x-2">
            <Tooltip content={t('common:edit', 'Sửa')} position="top">
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => handleEditClick(record)}
              />
            </Tooltip>
            <Tooltip content={t('common:delete', 'Xóa')} position="top">
              <IconCard
                icon="trash"
                variant="default"
                size="sm"
                onClick={() => handleDeleteClick(record.id)}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [t]
  );

  // Tạo hàm createQueryParams với useCallback để tránh re-render không cần thiết
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): OkrCycleQueryDto => {
    const queryParams: OkrCycleQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
      startDate: params.dateRange[0] || undefined,
      endDate: params.dateRange[1] || undefined,
    };

    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.status = params.filterValue as OkrCycleStatus;
    }

    if (params.dateRange[0] && params.dateRange[1]) {
      queryParams.startDate = params.dateRange[0];
      queryParams.endDate = params.dateRange[1];
    }

    return queryParams;
  }, []);

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<OkrCycleResponseDto, OkrCycleQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách chu kỳ OKR với queryParams từ dataTable
  const { data: cyclesData, isLoading, refetch } = useOkrCycles(dataTable.queryParams);

  // Mutation để xóa chu kỳ OKR
  const deleteCycleMutation = useDeleteOkrCycle();

  // Xử lý thêm mới
  const handleAdd = () => {
    setCycleToEdit(undefined);
    showForm();
  };

  // Xử lý click nút sửa
  const handleEditClick = (cycle: OkrCycleResponseDto) => {
    setCycleToEdit(cycle);
    showForm();
  };

  // Xử lý click nút xóa
  const handleDeleteClick = (id: number) => {
    setCycleToDelete(id);
    setIsDeleteModalOpen(true);
  };

  // Xử lý xác nhận xóa
  const handleConfirmDelete = () => {
    if (cycleToDelete) {
      deleteCycleMutation.mutate(cycleToDelete, {
        onSuccess: () => {
          NotificationUtil.success({
            message: t('okrs:cycle.messages.deleteSuccess', 'Xóa chu kỳ OKR thành công'),
          });
          setIsDeleteModalOpen(false);
          setCycleToDelete(null);
        },
        onError: () => {
          NotificationUtil.error({
            message: t('okrs:cycle.messages.deleteError', 'Xóa chu kỳ OKR thất bại'),
          });
        },
      });
    }
  };

  // Xử lý đóng form
  const handleCancel = () => {
    hideForm();
  };

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: (() => {
      // Map status to translation keys
      const statusTranslationMap = {
        [OkrCycleStatus.ACTIVE]: 'okrs:cycle.status.active',
        [OkrCycleStatus.PLANNING]: 'okrs:cycle.status.planning',
        [OkrCycleStatus.CLOSED]: 'okrs:cycle.status.closed',
      };

      // Default fallback values for each status
      const fallbackMap = {
        [OkrCycleStatus.ACTIVE]: 'Active',
        [OkrCycleStatus.PLANNING]: 'Planning',
        [OkrCycleStatus.CLOSED]: 'Closed',
      };

      return {
        [OkrCycleStatus.ACTIVE]: t(
          statusTranslationMap[OkrCycleStatus.ACTIVE],
          fallbackMap[OkrCycleStatus.ACTIVE]
        ),
        [OkrCycleStatus.PLANNING]: t(
          statusTranslationMap[OkrCycleStatus.PLANNING],
          fallbackMap[OkrCycleStatus.PLANNING]
        ),
        [OkrCycleStatus.CLOSED]: t(
          statusTranslationMap[OkrCycleStatus.CLOSED],
          fallbackMap[OkrCycleStatus.CLOSED]
        ),
      };
    })(),
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <SlideInForm isVisible={isVisible}>
        <OkrCycleForm
          cycle={cycleToEdit}
          onClose={handleCancel}
          onSuccess={() => refetch()}
          title={
            cycleToEdit
              ? t('okrs:cycle.form.editTitle', 'Cập nhật chu kỳ OKR')
              : t('okrs:cycle.form.title', 'Thêm chu kỳ OKR mới')
          }
        />
      </SlideInForm>

      {/* Modal xác nhận xóa */}
      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleConfirmDelete}
        title={t('okrs:cycle.delete.title', 'Xóa chu kỳ OKR')}
        message={t('okrs:cycle.delete.message', 'Bạn có chắc chắn muốn xóa chu kỳ OKR này?')}
        description={t(
          'okrs:cycle.delete.description',
          'Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến chu kỳ này sẽ bị xóa vĩnh viễn.'
        )}
        isLoading={deleteCycleMutation.isPending}
      />

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={cyclesData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: cyclesData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: cyclesData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default OkrCyclesPage;
