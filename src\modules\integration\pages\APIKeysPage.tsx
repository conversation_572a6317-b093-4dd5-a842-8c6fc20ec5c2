import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typo<PERSON>,
  Card,
  Button,
  SearchBar,
  Icon,
  Modal,
  Form,
  FormItem,
  FormGrid,
  Input,
  DatePicker,
  IconCard,
  Tooltip,
  ModernMenu,
  Container,
  Badge,
} from '@/shared/components/common';
import Table from '@/shared/components/common/Table';
import { TableColumn } from '@/shared/components/common/Table/types';

// Định nghĩa kiểu dữ liệu cho APIKey
interface APIKey {
  id: string;
  apiKey: string;
  description: string;
  expiredAt: string;
  isActive: boolean;
  createdAt: string;
  scope: string[];
  usageCount: number;
  lastUsed: string;
  createdBy: string;
  environment: string;
}

/**
 * Dữ liệu mẫu cho API keys
 */
const mockAPIKeys = [
  {
    id: '1',
    apiKey: 'sk_test_51NxXXXXXXXXXXXXXXXXXXXXXX',
    description: 'API key cho ứng dụng web',
    expiredAt: '2024-12-31T23:59:59Z',
    isActive: true,
    createdAt: '2023-01-05T08:30:00Z',
    scope: ['read', 'write'],
    usageCount: 1245,
    lastUsed: '2023-09-15T14:30:22Z',
    createdBy: '<EMAIL>',
    environment: 'production',
  },
  {
    id: '2',
    apiKey: 'sk_test_52NyYYYYYYYYYYYYYYYYYYYYYY',
    description: 'API key cho ứng dụng di động',
    expiredAt: '2024-10-15T23:59:59Z',
    isActive: true,
    createdAt: '2023-02-10T10:15:00Z',
    scope: ['read', 'write', 'delete'],
    usageCount: 3782,
    lastUsed: '2023-09-18T09:45:11Z',
    createdBy: '<EMAIL>',
    environment: 'production',
  },
  {
    id: '3',
    apiKey: 'sk_test_53NzZZZZZZZZZZZZZZZZZZZZZZ',
    description: 'API key cho tích hợp bên thứ ba',
    expiredAt: '2024-08-20T23:59:59Z',
    isActive: false,
    createdAt: '2023-03-15T14:45:00Z',
    scope: ['read'],
    usageCount: 567,
    lastUsed: '2023-07-22T16:20:45Z',
    createdBy: '<EMAIL>',
    environment: 'production',
  },
  {
    id: '4',
    apiKey: 'sk_test_54NaAAAAAAAAAAAAAAAAAAAAA',
    description: 'API key cho môi trường phát triển',
    expiredAt: '2024-06-25T23:59:59Z',
    isActive: true,
    createdAt: '2023-04-20T09:20:00Z',
    scope: ['read', 'write', 'delete', 'admin'],
    usageCount: 8921,
    lastUsed: '2023-09-19T11:30:15Z',
    createdBy: '<EMAIL>',
    environment: 'development',
  },
  {
    id: '5',
    apiKey: 'sk_test_55NbBBBBBBBBBBBBBBBBBBBBBB',
    description: 'API key cho môi trường thử nghiệm',
    expiredAt: '2024-04-30T23:59:59Z',
    isActive: true,
    createdAt: '2023-05-25T11:30:00Z',
    scope: ['read', 'write'],
    usageCount: 4532,
    lastUsed: '2023-09-17T08:15:30Z',
    createdBy: '<EMAIL>',
    environment: 'testing',
  },
  {
    id: '6',
    apiKey: 'sk_test_56NcCCCCCCCCCCCCCCCCCCCCCC',
    description: 'API key cho webhook integration',
    expiredAt: '2025-01-15T23:59:59Z',
    isActive: true,
    createdAt: '2023-06-10T13:45:00Z',
    scope: ['read', 'write', 'webhook'],
    usageCount: 2156,
    lastUsed: '2023-09-18T22:10:05Z',
    createdBy: '<EMAIL>',
    environment: 'production',
  },
  {
    id: '7',
    apiKey: 'sk_test_57NdDDDDDDDDDDDDDDDDDDDDDD',
    description: 'API key cho analytics service',
    expiredAt: '2024-11-30T23:59:59Z',
    isActive: true,
    createdAt: '2023-07-05T15:20:00Z',
    scope: ['read', 'analytics'],
    usageCount: 9874,
    lastUsed: '2023-09-19T05:45:12Z',
    createdBy: '<EMAIL>',
    environment: 'production',
  },
];

/**
 * Trang quản lý API keys
 */
const APIKeysPage: React.FC = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showModal, setShowModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [editingAPIKey, setEditingAPIKey] = useState<APIKey | null>(null);
  const [showActionMenu, setShowActionMenu] = useState<string | null>(null);
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  // Không sử dụng ref cho form

  // Lọc danh sách API keys theo từ khóa tìm kiếm và trạng thái
  const filteredAPIKeys = mockAPIKeys.filter(apiKey => {
    const matchesSearch = apiKey.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && apiKey.isActive) ||
      (statusFilter === 'inactive' && !apiKey.isActive);

    return matchesSearch && matchesStatus;
  });

  // Hàm để hiển thị API key che giấu
  const maskAPIKey = (key: string) => {
    return `${key.substring(0, 10)}...${key.substring(key.length - 4)}`;
  };

  // Hàm để định dạng ngày
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  // Cấu hình cột cho bảng
  const columns: TableColumn<APIKey>[] = [
    {
      title: t('integration.apiKeys.id', 'ID'),
      dataIndex: 'id',
      key: 'id',
      width: '5%',
    },
    {
      title: t('integration.apiKeys.apiKey', 'API Key'),
      dataIndex: 'apiKey',
      key: 'apiKey',
      width: '15%',
      render: (_: unknown, record: APIKey) => (
        <div className="flex items-center">
          <span className="font-mono">{maskAPIKey(record.apiKey)}</span>
          <Button
            size="sm"
            variant="ghost"
            className="ml-2"
            onClick={() => {
              navigator.clipboard.writeText(record.apiKey);
              // Hiển thị thông báo sao chép thành công
            }}
          >
            <Icon name="copy" size="sm" />
          </Button>
        </div>
      ),
    },
    {
      title: t('integration.apiKeys.description', 'Mô tả'),
      dataIndex: 'description',
      key: 'description',
      width: '15%',
    },
    {
      title: t('integration.apiKeys.scope', 'Quyền truy cập'),
      dataIndex: 'scope',
      key: 'scope',
      width: '15%',
      render: (_: unknown, record: APIKey) => (
        <div className="max-h-20 overflow-y-auto">
          {record.scope ? (
            <div className="flex flex-wrap gap-1">
              {record.scope.map((item, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs"
                >
                  {item}
                </span>
              ))}
            </div>
          ) : (
            <Typography variant="body2" color="muted" className="italic">
              {t('integration.apiKeys.noData', 'Không có dữ liệu')}
            </Typography>
          )}
        </div>
      ),
    },
    {
      title: t('integration.apiKeys.environment', 'Môi trường'),
      dataIndex: 'environment',
      key: 'environment',
      width: '10%',
      render: (_: unknown, record: APIKey) => {
        const colors = {
          production: 'bg-red-100 text-red-800',
          development: 'bg-blue-100 text-blue-800',
          testing: 'bg-yellow-100 text-yellow-800',
        };
        const color =
          colors[record.environment as keyof typeof colors] || 'bg-gray-100 text-gray-800';
        return <span className={`px-2 py-1 rounded text-sm ${color}`}>{record.environment}</span>;
      },
    },
    {
      title: t('integration.apiKeys.expiredAt', 'Ngày hết hạn'),
      dataIndex: 'expiredAt',
      key: 'expiredAt',
      width: '10%',
      render: (_: unknown, record: APIKey) => formatDate(record.expiredAt),
    },
    {
      title: t('integration.apiKeys.status', 'Trạng thái'),
      dataIndex: 'isActive',
      key: 'isActive',
      width: '10%',
      render: (_: unknown, record: APIKey) => (
        <Badge variant={record.isActive ? 'success' : 'danger'} size="sm">
          {record.isActive
            ? t('integration.apiKeys.active', 'Hoạt động')
            : t('integration.apiKeys.inactive', 'Vô hiệu')}
        </Badge>
      ),
    },
    {
      title: t('integration.apiKeys.actions', 'Hành động'),
      key: 'actions',
      width: '5%',
      render: (_: unknown, record: APIKey) => (
        <div className="relative">
          <IconCard
            icon="menu"
            variant="ghost"
            size="sm"
            onClick={() => setShowActionMenu(record.id)}
          />
          {showActionMenu === record.id && (
            <div className="absolute z-50" style={{ left: '-100px' }}>
              <ModernMenu
                isOpen={showActionMenu === record.id}
                onClose={() => setShowActionMenu(null)}
                placement="bottom"
                width="180px"
                items={[
                  {
                    id: 'toggle',
                    label: record.isActive
                      ? t('integration.apiKeys.disable', 'Vô hiệu hóa')
                      : t('integration.apiKeys.enable', 'Kích hoạt'),
                    icon: record.isActive ? 'x' : 'check',
                    onClick: () => handleToggleStatus(record),
                  },
                  {
                    id: 'delete',
                    label: t('common.delete', 'Xóa'),
                    icon: 'trash',
                    onClick: () => {
                      setEditingAPIKey(record);
                      setShowDeleteConfirm(true);
                    },
                  },
                ]}
              />
            </div>
          )}
        </div>
      ),
    },
  ];

  // Xử lý khi submit form tạo API Key mới
  const handleSubmit = (data: { description: string; expiredAt: string }) => {
    console.log('Form data:', data);
    // Giả lập API call
    alert(
      t('integration.apiKeys.createSuccess', 'Đã tạo API Key mới với mô tả: {{description}}', {
        description: data.description,
      })
    );
    setShowModal(false);
  };

  // Xử lý khi xác nhận xóa
  const handleDelete = () => {
    if (editingAPIKey) {
      // Giả lập API call
      alert(
        t('integration.apiKeys.deleteSuccess', 'Đã xóa API Key: {{apiKey}}', {
          apiKey: maskAPIKey(editingAPIKey.apiKey),
        })
      );
      setShowDeleteConfirm(false);
    }
  };

  // Xử lý khi vô hiệu hóa/kích hoạt API Key
  const handleToggleStatus = (apiKey: APIKey) => {
    // Giả lập API call
    alert(
      t('integration.apiKeys.toggleSuccess', 'Đã {{action}} API Key: {{apiKey}}', {
        action: apiKey.isActive
          ? t('integration.apiKeys.disable', 'vô hiệu hóa')
          : t('integration.apiKeys.enable', 'kích hoạt'),
        apiKey: maskAPIKey(apiKey.apiKey),
      })
    );
  };

  // Xử lý khi click vào icon tìm kiếm
  const handleSearchClick = () => {
    setShowSearch(!showSearch);
    if (showSearch && searchTerm) {
      setSearchTerm('');
    }
  };

  // Xử lý khi chọn bộ lọc
  const handleFilterSelect = (value: string) => {
    setStatusFilter(value);
    setShowFilterMenu(false);
  };

  return (
    <Container>
      <Typography variant="h4" className="mb-4">
        {t('integration.apiKeys.title', 'Quản lý API Keys')}
      </Typography>
      <Typography variant="body1" color="muted" className="mb-6">
        {t(
          'integration.apiKeys.description',
          'Quản lý các API Keys để tích hợp với các dịch vụ bên ngoài'
        )}
      </Typography>

      <div className="flex justify-between items-center mb-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
          <div className="flex items-center space-x-2">
            <Tooltip content={t('integration.apiKeys.addNew', 'Tạo API Key mới')} position="bottom">
              <IconCard icon="plus" variant="primary" onClick={() => setShowModal(true)} />
            </Tooltip>

            <Tooltip content={t('common.search', 'Tìm kiếm')} position="bottom">
              <IconCard
                icon="search"
                variant={showSearch ? 'primary' : 'default'}
                onClick={handleSearchClick}
                active={showSearch}
              />
            </Tooltip>

            <div className="relative">
              <Tooltip
                content={t('integration.apiKeys.filterByStatus', 'Lọc theo trạng thái')}
                position="right"
              >
                <IconCard
                  icon="filter"
                  variant={statusFilter !== 'all' ? 'primary' : 'default'}
                  onClick={() => setShowFilterMenu(!showFilterMenu)}
                  active={statusFilter !== 'all'}
                />

                {showFilterMenu && (
                  <ModernMenu
                    isOpen={showFilterMenu}
                    onClose={() => setShowFilterMenu(false)}
                    placement="bottom"
                    width="180px"
                    items={[
                      {
                        id: 'all',
                        label: t('integration.apiKeys.allStatuses', 'Tất cả trạng thái'),
                        icon: 'list',
                        onClick: () => handleFilterSelect('all'),
                      },
                      {
                        id: 'active',
                        label: t('integration.apiKeys.active', 'Đang hoạt động'),
                        icon: 'check',
                        onClick: () => handleFilterSelect('active'),
                      },
                      {
                        id: 'inactive',
                        label: t('integration.apiKeys.inactive', 'Không hoạt động'),
                        icon: 'x',
                        onClick: () => handleFilterSelect('inactive'),
                      },
                    ]}
                  />
                )}
              </Tooltip>
            </div>
          </div>

          <div className="w-full sm:w-auto">
            <SearchBar
              visible={showSearch}
              value={searchTerm}
              onChange={setSearchTerm}
              onToggle={handleSearchClick}
              maxWidth="100%"
              variant="flat"
              autoFocus={true}
              showSearchIcon={false}
              className="w-full"
              placeholder={t('integration.apiKeys.searchPlaceholder', 'Tìm kiếm theo mô tả...')}
            />
          </div>
        </div>
      </div>

      <Card className="mb-6 p-6">
        <div className="mb-4">
          <Typography variant="h5">
            {t('integration.apiKeys.list', 'Danh sách API Keys')}
          </Typography>
        </div>

        <Table columns={columns} data={filteredAPIKeys} rowKey="id" pagination />
      </Card>

      {/* Modal tạo API Key mới */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={t('integration.apiKeys.createNew', 'Tạo API Key mới')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowModal(false)}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button type="submit" form="apiKeyForm" variant="primary">
              {t('common.create', 'Tạo mới')}
            </Button>
          </div>
        }
      >
        <Form
          id="apiKeyForm"
          onSubmit={() => handleSubmit}
          defaultValues={{
            description: '',
            expiredAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 năm sau
          }}
        >
          <FormGrid columns={1} gap="md">
            <FormItem
              name="description"
              label={t('integration.apiKeys.description', 'Mô tả')}
              required
            >
              <Input
                placeholder={t(
                  'integration.apiKeys.descriptionPlaceholder',
                  'Nhập mô tả cho API Key'
                )}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="expiredAt"
              label={t('integration.apiKeys.expiredAt', 'Ngày hết hạn')}
              required
            >
              <DatePicker placeholder={t('integration.apiKeys.selectDate', 'Chọn ngày hết hạn')} />
            </FormItem>
          </FormGrid>
        </Form>
      </Modal>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleDelete}>
              {t('common.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography>
            {t(
              'integration.apiKeys.confirmDeleteMessage',
              'Bạn có chắc chắn muốn xóa API Key này?'
            )}
          </Typography>
          {editingAPIKey && (
            <Typography variant="body2" className="mt-2 font-mono">
              {maskAPIKey(editingAPIKey.apiKey)}
            </Typography>
          )}
        </div>
      </Modal>
    </Container>
  );
};

export default APIKeysPage;
