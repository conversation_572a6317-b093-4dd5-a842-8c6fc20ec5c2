import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import {
  Typography,
  Button,
  Icon,
  Modal,
  Pagination,
  Container,
} from '@/shared/components/common';
import {
  useGetWebsites,
  useConnectAgentToWebsite,
  useDisconnectAgentFromWebsite
} from '../website/hooks/useWebsite';
import { WebsiteQueryDto } from '../website/types/website.types';
import WebsiteManagementComponent from '../components/WebsiteManagementComponent';

// Interface cho Website với thông tin hiển thị
interface WebsiteDisplayData {
  id: string;
  host: string;
  verify: boolean;
  agentId?: string | null;
  agentName?: string | null;
  createdAt: string;
  // Thêm các thuộc tính để tương thích với LinkedAccountsCard
  bankCode: string;
  bankName: string;
  accountName: string;
  accountNumber: string;
  linkedDate: string;
  active: boolean;
}



/**
 * Trang hiển thị danh sách website đã tích hợp
 */
const WebsiteIntegrationPage: React.FC = () => {
  const { t } = useTranslation('integration');
  const { id: agentId } = useParams<{ id: string }>();

  // State management
  const [showToggleConfirm, setShowToggleConfirm] = useState(false);
  const [websiteToToggle, setWebsiteToToggle] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(4);
  const [filters, setFilters] = useState<WebsiteQueryDto>({
    page: 1,
    limit: 4,
  });

  // Query parameters for API
  const queryParams = useMemo<WebsiteQueryDto>(() => ({
    ...filters,
    page: currentPage,
    limit: itemsPerPage,
  }), [filters, currentPage, itemsPerPage]);

  // API hooks
  const { data: websitesData, isLoading, error, refetch } = useGetWebsites(queryParams);
  const connectAgentMutation = useConnectAgentToWebsite();
  const disconnectAgentMutation = useDisconnectAgentFromWebsite();

  // Transform API data to display format
  const websites = useMemo<WebsiteDisplayData[]>(() => {
    if (!websitesData?.result?.items) return [];

    return websitesData.result.items.map(website => ({
      id: website.id,
      host: website.host,
      verify: website.verify,
      agentId: website.agentId,
      agentName: website.agentName,
      createdAt: website.createdAt,
      // Transform to LinkedAccountsCard format
      bankCode: 'WEB',
      bankName: 'Website',
      accountName: website.host, // Sử dụng host làm account name
      accountNumber: website.host,
      linkedDate: new Date(parseInt(website.createdAt)).toLocaleDateString(),
      active: website.verify && !!website.agentId, // Active if verified and connected to agent
    }));
  }, [websitesData]);

  const totalItems = websitesData?.result?.meta?.totalItems || 0;

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Handle toggle website (connect/disconnect agent)
  const handleToggleWebsite = async () => {
    if (!websiteToToggle || !agentId) return;

    const website = websites.find(w => w.id === websiteToToggle);
    if (!website) return;

    try {
      if (website.active && website.agentId) {
        // Disconnect agent from website
        await disconnectAgentMutation.mutateAsync({
          websiteId: websiteToToggle,
        });
      } else {
        // Connect agent to website
        await connectAgentMutation.mutateAsync({
          agentId,
          websiteId: websiteToToggle,
        });
      }

      setShowToggleConfirm(false);
      setWebsiteToToggle(null);
    } catch (error) {
      console.error('Error toggling website:', error);
      alert(t('common.error', 'Đã xảy ra lỗi'));
    }
  };

  // Cancel toggle
  const handleCancelToggle = () => {
    setShowToggleConfirm(false);
    setWebsiteToToggle(null);
  };

  // Handle filter change
  const handleFilterChange = (newFilters: WebsiteQueryDto) => {
    setFilters(newFilters);
    // Reset về trang đầu khi filter thay đổi
    if (newFilters.search !== filters.search ||
        newFilters.sortBy !== filters.sortBy ||
        newFilters.sortDirection !== filters.sortDirection ||
        newFilters.verify !== filters.verify) {
      setCurrentPage(1);
    }
  };

  return (
    <Container>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.history.back()}
            className="flex items-center space-x-2"
          >
            <Icon name="arrow-left" size="sm" />
            <span>{t('common.back', 'Quay lại')}</span>
          </Button>
          <Icon name="website" size="lg" className="text-primary" />
          <Typography variant="h4">
            {t('website.title', 'Tích hợp Website')}
          </Typography>
        </div>
        <Typography variant="body1" color="muted" className="mt-2">
          {t('website.description', 'Quản lý các website đã tích hợp với AI Agent')}
        </Typography>
      </div>

      {/* Website Management Component */}
      <WebsiteManagementComponent
        result={websitesData?.result}
        isLoading={isLoading}
        onRefresh={refetch}
        onFilterChange={handleFilterChange}
        currentFilters={filters}
      />

      {/* Pagination - chỉ hiển thị khi có dữ liệu */}
      {!isLoading && !error && websites.length > 0 && (
        <div className="mt-6 flex justify-end">
          <Pagination
            variant="compact"
            borderless={true}
            currentPage={currentPage}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}

      {/* Confirmation Modal */}
      <Modal
        isOpen={showToggleConfirm}
        onClose={handleCancelToggle}
        title={t('common.confirm', 'Xác nhận')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancelToggle}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button
              variant={websites.find(w => w.id === websiteToToggle)?.active ? 'danger' : 'primary'}
              onClick={handleToggleWebsite}
              isLoading={connectAgentMutation.isPending || disconnectAgentMutation.isPending}
              disabled={connectAgentMutation.isPending || disconnectAgentMutation.isPending}
            >
              {websites.find(w => w.id === websiteToToggle)?.active
                ? t('common.deactivate', 'Tắt')
                : t('common.activate', 'Bật')}
            </Button>
          </div>
        }
      >
        <div className="p-4">
          <Typography>
            {websites.find(w => w.id === websiteToToggle)?.active
              ? t('website.confirmDeactivate', 'Bạn có chắc chắn muốn tắt website này?')
              : t('website.confirmActivate', 'Bạn có chắc chắn muốn bật website này?')}
          </Typography>
        </div>
      </Modal>

    </Container>
  );
};

export default WebsiteIntegrationPage;
