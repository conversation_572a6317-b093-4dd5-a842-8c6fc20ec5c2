import { format } from 'date-fns';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ActiveFilters } from '@/modules/components/filters';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, Icon, Table } from '@/shared/components/common';
import ActionMenu from '@/shared/components/common/ActionMenu';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useBulkSelection } from '@/shared/hooks/useBulkSelection';
import useSlideForm from '@/shared/hooks/useSlideForm';

import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';
import CreateEmployeeForm from '../components/employee/CreateEmployeeForm';
import CreateUserForEmployeeForm from '../components/employee/CreateUserForEmployeeForm';
import EditEmployeeForm from '../components/employee/EditEmployeeForm';
import EmployeeDirectPermission from '../components/employee/EmployeeDirectPermission';
import EmployeePermission from '../components/employee/EmployeePermission';
import EmployeeStatistics from '../components/employee/EmployeeStatistics';
import { useDepartmentMapping } from '../hooks/useDepartmentMapping';
import {
  useBulkDeleteEmployees,
  useCreateEmployee,
  useEmployee,
  useEmployees,
  useUpdateEmployee
} from '../hooks/useEmployees';
import {
  CreateEmployeeDto,
  EmployeeDto,
  EmployeeQueryDto,
  EmployeeStatus,
  UpdateEmployeeDto,
} from '../types/employee.types';

/**
 * Trang quản lý nhân viên
 */
const EmployeesPage: React.FC = () => {
  const { t } = useTranslation(['hrm', 'common']);

  // Sử dụng hook animation cho các form
  const { isVisible, showForm, hideForm } = useSlideForm(); // Form thêm nhân viên
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm(); // Form sửa nhân viên
  const {
    isVisible: isRoleFormVisible,
    showForm: showRoleForm,
    hideForm: hideRoleForm,
  } = useSlideForm(); // Form phân quyền vai trò
  const {
    isVisible: isPermissionFormVisible,
    hideForm: hidePermissionForm,
  } = useSlideForm(); // Form phân quyền trực tiếp
  const {
    isVisible: isCreateUserFormVisible,
    showForm: showCreateUserForm,
    hideForm: hideCreateUserForm,
  } = useSlideForm(); // Form tạo tài khoản người dùng

  // State cho form phân quyền và tạo tài khoản
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<number | null>(null);

  // State cho tab view
  const [activeTab, setActiveTab] = useState<string>('list');

  // Định nghĩa các tùy chọn bộ lọc với icon phù hợp
  const filterOptions = useMemo(
    () => [
      {
        id: 'all',
        label: t('common:all'),
        value: 'all',
        icon: 'list' as const
      },
      {
        id: EmployeeStatus.ACTIVE,
        label: t('hrm:employee.status.active', 'Đang hoạt động'),
        value: EmployeeStatus.ACTIVE,
        icon: 'check' as const
      },
      {
        id: EmployeeStatus.INACTIVE,
        label: t('hrm:employee.status.inactive', 'Không hoạt động'),
        value: EmployeeStatus.INACTIVE,
        icon: 'x' as const
      },
      {
        id: EmployeeStatus.ON_LEAVE,
        label: t('hrm:employee.status.on_leave', 'Nghỉ phép'),
        value: EmployeeStatus.ON_LEAVE,
        icon: 'calendar' as const
      },
      {
        id: EmployeeStatus.PROBATION,
        label: t('hrm:employee.status.probation', 'Thử việc'),
        value: EmployeeStatus.PROBATION,
        icon: 'clock' as const
      },
      {
        id: EmployeeStatus.TERMINATED,
        label: t('hrm:employee.status.terminated', 'Đã nghỉ việc'),
        value: EmployeeStatus.TERMINATED,
        icon: 'user-x' as const
      },
    ],
    [t]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<EmployeeDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'employeeName',
        title: t('hrm:employee.table.employeeName', 'Tên nhân viên'),
        dataIndex: 'employeeName',
        width: '15%',
        sortable: true,
      },
      {
        key: 'jobTitle',
        title: t('hrm:employee.table.jobTitle', 'Chức danh'),
        dataIndex: 'jobTitle',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div>{value ? String(value) : '-'}</div>;
        },
      },
      {
        key: 'department',
        title: t('hrm:employee.table.department', 'Phòng ban'),
        dataIndex: 'department',
        width: '15%',
        sortable: true,
        render: (_: unknown, record: EmployeeDto) => {
          // Hiển thị tên phòng ban từ department object hoặc mapping
          if (record.department?.name) {
            return <div>{record.department.name}</div>;
          }
          return <div>{getDepartmentName(record.departmentId)}</div>;
        },
      },
      {
        key: 'status',
        title: t('hrm:employee.table.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as string;
          let className = '';
          let label = '';

          switch (status) {
            case EmployeeStatus.ACTIVE:
              className = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
              label = t('hrm:employee.status.active', 'Đang hoạt động');
              break;
            case EmployeeStatus.INACTIVE:
              className = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
              label = t('hrm:employee.status.inactive', 'Không hoạt động');
              break;
            case EmployeeStatus.ON_LEAVE:
              className = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
              label = t('hrm:employee.status.on_leave', 'Nghỉ phép');
              break;
            case EmployeeStatus.PROBATION:
              className = 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
              label = t('hrm:employee.status.probation', 'Thử việc');
              break;
            case EmployeeStatus.TERMINATED:
              className = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
              label = t('hrm:employee.status.terminated', 'Đã nghỉ việc');
              break;
            default:
              className = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
              label = status || t('common:unknown', 'Không xác định');
          }

          return (
            <div className={`px-2 py-1 rounded-full text-center text-xs font-medium ${className}`}>
              {label}
            </div>
          );
        },
      },
      {
        key: 'hireDate',
        title: t('hrm:employee.table.hireDate', 'Ngày vào làm'),
        dataIndex: 'hireDate',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const date = value as string;
          if (!date) return <div>-</div>;
          try {
            const dateObj = new Date(date);
            if (isNaN(dateObj.getTime())) return <div>-</div>;
            return <div>{format(dateObj, 'dd/MM/yyyy')}</div>;
          } catch {
            return <div>-</div>;
          }
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '15%',
        render: (_: unknown, record: EmployeeDto) => {
          const actionItems = [
            {
              id: 'edit',
              label: t('common:edit', 'Sửa'),
              icon: 'edit',
              onClick: () => handleOpenEditForm(record.id),
            },
            {
              id: 'roles',
              label: t('hrm:employee.actions.roles', 'Vai trò'),
              icon: 'users',
              onClick: () => handleOpenRoleForm(record.id),
            },
          ];

          // Thêm action tạo tài khoản người dùng
          const rowActionItems = [
            ...actionItems,
            {
              id: 'create-user',
              label: t('hrm:employee.actions.createUser', 'Tạo tài khoản người dùng'),
              icon: 'user',
              onClick: () => handleOpenCreateUserForm(record.id),
            },
          ];

          return (
            <div className="flex justify-center">
              <ActionMenu
                items={rowActionItems}
                menuTooltip={t('common:moreActions', 'Thêm hành động')} // Thêm fallback text
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuIcon="more-horizontal" // Sử dụng icon ellipsis ngang
                showAllInMenu={true} // Hiển thị tất cả action trong menu
                preferRight={true} // Ưu tiên hiển thị menu bên phải nếu không đủ không gian bên trái
                preferTop={true} // Ưu tiên hiển thị menu bên trên nếu không đủ không gian bên dưới
              />
            </div>
          );
        },
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): EmployeeQueryDto => {
    const queryParams: EmployeeQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.status = params.filterValue as EmployeeStatus;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<EmployeeDto, EmployeeQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách nhân viên với queryParams từ dataTable
  const { data: employeesData, isLoading } = useEmployees(dataTable.queryParams);

  // Hook để lấy chi tiết nhân viên khi sửa
  const { data: selectedEmployeeData } = useEmployee(selectedEmployeeId || 0);

  // Hook để mapping department ID sang tên phòng ban
  const { getDepartmentName } = useDepartmentMapping();

  // Hook để tạo nhân viên mới
  const createEmployeeMutation = useCreateEmployee();

  // Hook để cập nhật nhân viên
  const updateEmployeeMutation = useUpdateEmployee();

  // Hook để xóa nhiều nhân viên
  const bulkDeleteEmployees = useBulkDeleteEmployees();

  // Sử dụng custom hook để quản lý bulk selection
  const {
    selectedRowKeys,
    setSelectedRowKeys,
    showBulkDeleteConfirm,
    deleteCount,
    handleShowBulkDeleteConfirm,
    handleCancelBulkDelete,
    handleConfirmBulkDelete,
    isDeleting,
  } = useBulkSelection({
    data: employeesData,
    bulkDeleteMutation: bulkDeleteEmployees,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    console.log('Form values:', values);

    // Chuyển đổi values thành CreateEmployeeDto
    const employeeData = values as unknown as CreateEmployeeDto;

    // Gọi API tạo nhân viên
    createEmployeeMutation.mutate(employeeData, {
      onSuccess: () => {
        hideForm();
        // Có thể thêm thông báo thành công ở đây
      },
      onError: (error: any) => {
        console.error('Error creating employee:', error);
        // Có thể thêm thông báo lỗi ở đây
      },
    });
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  // Xử lý mở form sửa nhân viên
  const handleOpenEditForm = (employeeId: number) => {
    setSelectedEmployeeId(employeeId);
    showEditForm();
  };

  // Xử lý đóng form sửa nhân viên
  const handleCloseEditForm = () => {
    hideEditForm();
    setTimeout(() => setSelectedEmployeeId(null), 300); // Đợi animation kết thúc
  };

  // Xử lý submit form sửa nhân viên
  const handleEditSubmit = (values: Record<string, unknown>) => {
    if (!selectedEmployeeId) {return;}

    console.log('Edit form values:', values);

    // Chuyển đổi values thành UpdateEmployeeDto
    const employeeData = values as unknown as UpdateEmployeeDto;

    // Gọi API cập nhật nhân viên
    updateEmployeeMutation.mutate(
      { id: selectedEmployeeId, data: employeeData },
      {
        onSuccess: () => {
          handleCloseEditForm();
          // Có thể thêm thông báo thành công ở đây
        },
        onError: (error: any) => {
          console.error('Error updating employee:', error);
          // Có thể thêm thông báo lỗi ở đây
        },
      }
    );
  };

  // Xử lý mở form phân quyền vai trò
  const handleOpenRoleForm = (employeeId: number) => {
    setSelectedEmployeeId(employeeId);
    showRoleForm();
  };

  // Xử lý đóng form phân quyền vai trò
  const handleCloseRoleForm = () => {
    hideRoleForm();
    setTimeout(() => setSelectedEmployeeId(null), 300); // Đợi animation kết thúc
  };

  // Xử lý đóng form phân quyền trực tiếp
  const handleClosePermissionForm = () => {
    hidePermissionForm();
    setTimeout(() => setSelectedEmployeeId(null), 300); // Đợi animation kết thúc
  };

  // Xử lý mở form tạo tài khoản người dùng
  const handleOpenCreateUserForm = (employeeId?: number) => {
    if (employeeId) {
      setSelectedEmployeeId(employeeId);
    }
    showCreateUserForm();
  };

  // Xử lý đóng form tạo tài khoản người dùng
  const handleCloseCreateUserForm = () => {
    hideCreateUserForm();
    setTimeout(() => setSelectedEmployeeId(null), 300); // Đợi animation kết thúc
  };

  // Xử lý khi tạo tài khoản thành công
  const handleCreateUserSuccess = () => {
    hideCreateUserForm();
    setTimeout(() => setSelectedEmployeeId(null), 300); // Đợi animation kết thúc
    // Có thể thêm thông báo thành công ở đây
  };

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [EmployeeStatus.ACTIVE]: t('hrm:employee.status.active', 'Đang hoạt động'),
      [EmployeeStatus.INACTIVE]: t('hrm:employee.status.inactive', 'Không hoạt động'),
      [EmployeeStatus.ON_LEAVE]: t('hrm:employee.status.on_leave', 'Nghỉ phép'),
      [EmployeeStatus.PROBATION]: t('hrm:employee.status.probation', 'Thử việc'),
      [EmployeeStatus.TERMINATED]: t('hrm:employee.status.terminated', 'Đã nghỉ việc'),
    },
    t,
  });

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          onAdd={handleAdd}
          items={dataTable.menuItems}
          onDateRangeChange={dataTable.dateRange.setDateRange}
          onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
          columns={dataTable.columnVisibility.visibleColumns}
          showDateFilter={false}
          showColumnFilter={true}
          additionalIcons={[
            {
              icon: 'trash',
              tooltip: t('common:bulkDelete'),
              variant: 'primary',
              onClick: handleShowBulkDeleteConfirm,
              className: 'text-red-500',
              condition: selectedRowKeys.length > 0,
            },
            {
              icon: 'list',
              tooltip: t('hrm:employee.tabs.list', 'Danh sách'),
              onClick: () => setActiveTab('list'),
              variant: activeTab === 'list' ? 'primary' : 'default',
            },
            {
              icon: 'bar-chart',
              tooltip: t('hrm:employee.tabs.stats', 'Thống kê'),
              onClick: () => setActiveTab('stats'),
              variant: activeTab === 'stats' ? 'primary' : 'default',
            },
            {
              icon: 'user',
              tooltip: t('hrm:employee.actions.createUser', 'Tạo tài khoản người dùng'),
              onClick: () => handleOpenCreateUserForm(),
              variant: 'primary',
            },
          ]}
        />
      </div>

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm nhân viên */}
      <SlideInForm isVisible={isVisible}>
        <CreateEmployeeForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={createEmployeeMutation.isPending}
        />
      </SlideInForm>

      {/* Form sửa nhân viên */}
      <SlideInForm isVisible={isEditFormVisible}>
        {selectedEmployeeData && (
          <EditEmployeeForm
            initialData={selectedEmployeeData}
            onSubmit={handleEditSubmit}
            onCancel={handleCloseEditForm}
            isSubmitting={updateEmployeeMutation.isPending}
          />
        )}
      </SlideInForm>

      {/* Form phân quyền vai trò */}
      <SlideInForm isVisible={isRoleFormVisible}>
        <div className="p-6 bg-background text-foreground rounded-md shadow-md">
          {selectedEmployeeId && (
            <EmployeePermission employeeId={selectedEmployeeId} onClose={handleCloseRoleForm} />
          )}
        </div>
      </SlideInForm>

      {/* Form phân quyền trực tiếp */}
      <SlideInForm isVisible={isPermissionFormVisible}>
        <div className="p-6 bg-background text-foreground rounded-md shadow-md">
          <h2 className="text-xl font-semibold mb-4 text-primary">
            {t('hrm:employee.permissionModal.title', 'Phân quyền trực tiếp')}
          </h2>
          {selectedEmployeeId && (
            <EmployeeDirectPermission
              employeeId={selectedEmployeeId}
              onClose={handleClosePermissionForm}
            />
          )}
        </div>
      </SlideInForm>

      {/* Form tạo tài khoản người dùng */}
      <SlideInForm isVisible={isCreateUserFormVisible}>
        <CreateUserForEmployeeForm
          employeeId={selectedEmployeeId || undefined}
          onSuccess={handleCreateUserSuccess}
          onCancel={handleCloseCreateUserForm}
        />
      </SlideInForm>

      {/* Hiển thị content theo tab */}
      {activeTab === 'list' && (
        <Card className="overflow-hidden">
          <Table
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={employeesData?.items || []}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            selectable
            rowSelection={{
              selectedRowKeys,
              onChange: keys => setSelectedRowKeys(keys),
            }}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: employeesData?.meta.currentPage || 1,
              pageSize: dataTable.tableData.pageSize,
              total: employeesData?.meta.totalItems || 0,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 15, 20],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      )}

      {activeTab === 'grid' && (
        <Card className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {employeesData?.items?.map(employee => (
              <div
                key={employee.id}
                className="p-4 border border-border rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <Icon name="user" size="sm" />
                  </div>
                  <div>
                    <h3 className="font-medium text-sm">{employee.employeeName}</h3>
                    <p className="text-xs text-muted-foreground">{employee.jobTitle || '-'}</p>
                  </div>
                </div>
                <div className="space-y-1 text-xs">
                  <p>
                    <span className="text-muted-foreground">Phòng ban:</span>{' '}
                    {employee.department?.name || getDepartmentName(employee.departmentId)}
                  </p>
                  <p>
                    <span className="text-muted-foreground">Ngày vào:</span>{' '}
                    {employee.hireDate ? (() => {
                      try {
                        const dateObj = new Date(employee.hireDate);
                        if (isNaN(dateObj.getTime())) return '-';
                        return format(dateObj, 'dd/MM/yyyy');
                      } catch {
                        return '-';
                      }
                    })() : '-'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {activeTab === 'stats' && <EmployeeStatistics />}

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete')}
        message={t(
          'hrm:employee.bulkDeleteConfirmMessage',
          'Bạn có chắc chắn muốn xóa {{count}} nhân viên đã chọn?',
          { count: deleteCount }
        )}
        isSubmitting={isDeleting}
      />
    </div>
  );
};

export default EmployeesPage;
