import { TFunction } from 'i18next';
import { z } from 'zod';

/**
 * Schema cho form tạo tài khoản người dùng cho nhân viên
 * @param t Hàm dịch
 * @param autoGeneratePassword Có tự động tạo mật khẩu không
 * @returns Schema Zod
 */
export const createUserForEmployeeSchema = (t: TFunction, autoGeneratePassword = false) => {
  const baseSchema = z.object({
    email: z
      .string()
      .min(1, t('hrm:employee.form.validation.emailRequired', 'Email là bắt buộc'))
      .email(t('hrm:employee.form.validation.emailInvalid', 'Email không hợp lệ')),
    employeeId: z
      .number()
      .min(1, t('hrm:employee.form.validation.employeeRequired', '<PERSON>ui lòng chọn nhân viên')),
  });

  // Nếu không auto generate password, thêm các trường bắt buộc
  if (!autoGeneratePassword) {
    return baseSchema.extend({
      username: z
        .string()
        .min(4, t('hrm:employee.form.validation.usernameMinLength', 'Tên đăng nhập phải có ít nhất 4 ký tự'))
        .max(50, t('hrm:employee.form.validation.usernameMaxLength', 'Tên đăng nhập không được vượt quá 50 ký tự'))
        .regex(/^\w+$/, t('hrm:employee.form.validation.usernameFormat', 'Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới')),
    
    password: z
      .string()
      .min(8, t('hrm:employee.form.validation.passwordMinLength', 'Mật khẩu phải có ít nhất 8 ký tự'))
      .max(50, t('hrm:employee.form.validation.passwordMaxLength', 'Mật khẩu không được vượt quá 50 ký tự'))
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!$%&*?@])[\d!$%&*?@A-Za-z]{8,}$/,
        t('hrm:employee.form.validation.passwordFormat', 'Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt')
      ),
    
    confirmPassword: z.string().min(1, t('hrm:employee.form.validation.confirmPasswordRequired', 'Xác nhận mật khẩu là bắt buộc')),
    
    email: z
      .string()
      .min(1, t('hrm:employee.form.validation.emailRequired', 'Email là bắt buộc'))
      .email(t('hrm:employee.form.validation.emailFormat', 'Email không hợp lệ'))
      .max(255, t('hrm:employee.form.validation.emailMaxLength', 'Email không được vượt quá 255 ký tự')),
    
    fullName: z
      .string()
      .min(1, t('hrm:employee.form.validation.fullNameRequired', 'Họ và tên đầy đủ là bắt buộc'))
      .max(255, t('hrm:employee.form.validation.fullNameMaxLength', 'Họ và tên không được vượt quá 255 ký tự')),
    
    departmentId: z.number().optional(),
    employeeId: z.number().optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: t('hrm:employee.form.validation.passwordsMatch', 'Mật khẩu và xác nhận mật khẩu không khớp'),
    path: ['confirmPassword'],
  });

/**
 * Kiểu dữ liệu cho form tạo tài khoản người dùng
 */
export type CreateUserForEmployeeFormValues = z.infer<ReturnType<typeof createUserForEmployeeSchema>>;
