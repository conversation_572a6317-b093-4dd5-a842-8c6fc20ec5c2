import { useState, useEffect } from 'react';
import { Typography, Modal } from '@/shared/components/common';
import BankAccountInfo from '../components/BankAccountInfo';
import IndustryInfoForm, { IndustryInfoFormData } from '../components/IndustryInfoForm';
import LinkedAccountsCard from '../components/LinkedAccountsCard';
import { fetchLinkedAccounts, fetchAccountById } from '../api/accountMockData';
import { BankAccount } from '../types/account';

const PaymentGatewaysPage = () => {
  // State cho modal thông tin ngành hàng
  const [showIndustryInfoModal, setShowIndustryInfoModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State cho tài khoản được chọn
  const [selectedAccount, setSelectedAccount] = useState<BankAccount | null>(null);

  // L<PERSON>y danh sách tài khoản khi component được mount
  const [accounts, setAccounts] = useState<BankAccount[]>([]);
  const [, setIsLoadingAccounts] = useState(true);

  useEffect(() => {
    const loadAccounts = async () => {
      try {
        setIsLoadingAccounts(true);
        const data = await fetchLinkedAccounts();
        setAccounts(data);
        console.log('Đã tải danh sách tài khoản:', data);
      } catch (error) {
        console.error('Lỗi khi tải danh sách tài khoản:', error);
      } finally {
        setIsLoadingAccounts(false);
      }
    };

    loadAccounts();
  }, []);

  // Xử lý khi click vào một tài khoản
  const handleAccountClick = async (id: string) => {
    try {
      const account = await fetchAccountById(id);
      setSelectedAccount(account);
    } catch (error) {
      console.error('Lỗi khi lấy thông tin tài khoản:', error);
    }
  };

  // Xử lý khi click vào nút xem chi tiết
  const handleViewDetails = (id: string) => {
    handleAccountClick(id);
  };

  // Xử lý khi submit form thông tin ngành hàng
  const handleIndustryInfoSubmit = (data: IndustryInfoFormData) => {
    setIsSubmitting(true);

    // Giả lập API call
    console.log('Form data:', data);
    setTimeout(() => {
      setIsSubmitting(false);
      setShowIndustryInfoModal(false);
      alert('Kết nối ngành hàng thành công!');
    }, 1500);
  };

  return (
    <div>
      {/* Layout chính với tỷ lệ 1/3 và 2/3 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Cột bên trái (1/3) - Danh sách tài khoản */}
        <div className="md:col-span-1">
          <Typography variant="h5" className="mb-4">
            Tài khoản liên kết
          </Typography>
          <LinkedAccountsCard
            accounts={accounts}
            onAddClick={() => alert('Thêm tài khoản mới')}
            onDeleteClick={handleViewDetails}
            actionTitle="Xem chi tiết"
            showDeleteButtons={false}
          />
        </div>

        {/* Cột bên phải (2/3) - Thông tin ngành hàng hoặc chi tiết tài khoản */}
        <div className="md:col-span-2">
          {selectedAccount ? (
            // Hiển thị thông tin chi tiết tài khoản khi có tài khoản được chọn
            <BankAccountInfo
              bankName={selectedAccount.bankName}
              accountName={selectedAccount.accountName}
              accountNumber={selectedAccount.accountNumber}
              connectionMethod="API Banking"
              balance="0,00 đ"
              onConnect={() => alert('Kết nối API Banking')}
            />
          ) : (
            // Hiển thị form thông tin ngành hàng khi không có tài khoản được chọn
            <IndustryInfoForm onSubmit={handleIndustryInfoSubmit} isLoading={isSubmitting} />
          )}
        </div>
      </div>

      {/* Modal hiển thị form thông tin ngành hàng */}
      <Modal
        isOpen={showIndustryInfoModal}
        onClose={() => !isSubmitting && setShowIndustryInfoModal(false)}
        title="Thông tin ngành hàng"
        size="xl"
      >
        <IndustryInfoForm onSubmit={handleIndustryInfoSubmit} isLoading={isSubmitting} />
      </Modal>
    </div>
  );
};

export default PaymentGatewaysPage;
