import { get } from 'lodash';
import { useState, useCallback, useMemo, useEffect } from 'react';

interface UseTableSelectionOptions<T> {
  /**
   * Dữ liệu hiển thị
   */
  data: T[];

  /**
   * Khóa duy nhất cho mỗi hàng
   */
  rowKey?: string | ((record: T) => string);

  /**
   * Các khóa đã chọn mặc định
   */
  defaultSelectedRowKeys?: React.Key[];

  /**
   * Callback khi thay đổi lựa chọn
   */
  onChange?: (selectedRowKeys: React.Key[], selectedRows: T[]) => void;

  /**
   * Hàm lấy thuộc tính checkbox
   */
  getCheckboxProps?: (record: T) => { disabled?: boolean; name?: string };
}

interface UseTableSelectionResult<T> {
  /**
   * Các khóa đã chọn
   */
  selectedRowKeys: React.Key[];

  /**
   * <PERSON><PERSON><PERSON> hàng đã chọn
   */
  selectedRows: T[];

  /**
   * Hàm chọn tất cả
   */
  selectAll: () => void;

  /**
   * Hàm bỏ chọn tất cả
   */
  deselectAll: () => void;

  /**
   * Hàm chọn một hàng
   */
  selectRow: (key: React.Key) => void;

  /**
   * Hàm bỏ chọn một hàng
   */
  deselectRow: (key: React.Key) => void;

  /**
   * Hàm kiểm tra xem một hàng có được chọn không
   */
  isSelected: (key: React.Key) => boolean;

  /**
   * Hàm kiểm tra xem tất cả các hàng có được chọn không
   */
  isAllSelected: boolean;

  /**
   * Hàm kiểm tra xem một hàng có bị vô hiệu hóa không
   */
  isDisabled: (record: T) => boolean;
}

/**
 * Hook xử lý chọn hàng trong bảng
 */
export function useTableSelection<T>({
  data,
  rowKey = 'id',
  defaultSelectedRowKeys = [],
  onChange,
  getCheckboxProps,
}: UseTableSelectionOptions<T>): UseTableSelectionResult<T> {
  // State lưu trữ các khóa đã chọn
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>(defaultSelectedRowKeys);

  // Sync với selectedRowKeys từ props khi thay đổi
  useEffect(() => {
    setSelectedRowKeys(defaultSelectedRowKeys);
  }, [defaultSelectedRowKeys]);

  // Lấy khóa duy nhất cho mỗi hàng
  const getRowKeyValue = useCallback(
    (record: T): React.Key => {
      if (typeof rowKey === 'function') {
        return rowKey(record);
      }
      return get(record, rowKey).toString();
    },
    [rowKey]
  );

  // Lấy các hàng đã chọn
  const selectedRows = useMemo(() => {
    return data.filter(record => {
      const key = getRowKeyValue(record);
      return selectedRowKeys.includes(key);
    });
  }, [data, selectedRowKeys, getRowKeyValue]);

  // Kiểm tra xem một hàng có bị vô hiệu hóa không
  const isDisabled = useCallback(
    (record: T): boolean => {
      if (!getCheckboxProps) {
        return false;
      }
      const props = getCheckboxProps(record);
      return !!props.disabled;
    },
    [getCheckboxProps]
  );

  // Lấy các hàng có thể chọn
  const selectableRows = useMemo(() => {
    return data.filter(record => !isDisabled(record));
  }, [data, isDisabled]);

  // Lấy các khóa có thể chọn
  const selectableKeys = useMemo(() => {
    return selectableRows.map(getRowKeyValue);
  }, [selectableRows, getRowKeyValue]);

  // Kiểm tra xem tất cả các hàng có được chọn không
  const isAllSelected = useMemo(() => {
    return selectableKeys.length > 0 && selectableKeys.every(key => selectedRowKeys.includes(key));
  }, [selectableKeys, selectedRowKeys]);

  // Hàm chọn tất cả
  const selectAll = useCallback(() => {
    const newSelectedRowKeys = [...selectedRowKeys];

    // Thêm các khóa chưa được chọn
    for (const key of selectableKeys) {
      if (!newSelectedRowKeys.includes(key)) {
        newSelectedRowKeys.push(key);
      }
    }

    setSelectedRowKeys(newSelectedRowKeys);
    onChange?.(
      newSelectedRowKeys,
      data.filter(record => newSelectedRowKeys.includes(getRowKeyValue(record)))
    );
  }, [selectedRowKeys, selectableKeys, onChange, data, getRowKeyValue]);

  // Hàm bỏ chọn tất cả
  const deselectAll = useCallback(() => {
    const newSelectedRowKeys = selectedRowKeys.filter(key => !selectableKeys.includes(key));

    setSelectedRowKeys(newSelectedRowKeys);
    onChange?.(
      newSelectedRowKeys,
      data.filter(record => newSelectedRowKeys.includes(getRowKeyValue(record)))
    );
  }, [selectedRowKeys, selectableKeys, onChange, data, getRowKeyValue]);

  // Hàm chọn một hàng
  const selectRow = useCallback(
    (key: React.Key) => {
      if (selectedRowKeys.includes(key)) {
        return;
      }

      const newSelectedRowKeys = [...selectedRowKeys, key];
      setSelectedRowKeys(newSelectedRowKeys);
      onChange?.(
        newSelectedRowKeys,
        data.filter(record => newSelectedRowKeys.includes(getRowKeyValue(record)))
      );
    },
    [selectedRowKeys, onChange, data, getRowKeyValue]
  );

  // Hàm bỏ chọn một hàng
  const deselectRow = useCallback(
    (key: React.Key) => {
      if (!selectedRowKeys.includes(key)) {
        return;
      }

      const newSelectedRowKeys = selectedRowKeys.filter(k => k !== key);
      setSelectedRowKeys(newSelectedRowKeys);
      onChange?.(
        newSelectedRowKeys,
        data.filter(record => newSelectedRowKeys.includes(getRowKeyValue(record)))
      );
    },
    [selectedRowKeys, onChange, data, getRowKeyValue]
  );

  // Hàm kiểm tra xem một hàng có được chọn không
  const isSelected = useCallback(
    (key: React.Key) => {
      return selectedRowKeys.includes(key);
    },
    [selectedRowKeys]
  );

  return {
    selectedRowKeys,
    selectedRows,
    selectAll,
    deselectAll,
    selectRow,
    deselectRow,
    isSelected,
    isAllSelected,
    isDisabled,
  };
}
