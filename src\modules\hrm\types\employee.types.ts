import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Enum định nghĩa các trạng thái nhân viên
 */
export enum EmployeeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ON_LEAVE = 'on_leave',
  TERMINATED = 'terminated',
  PROBATION = 'probation',
  SUSPENDED = 'suspended',
}

/**
 * Enum định nghĩa các loại hợp đồng
 */
export enum EmploymentType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  CONTRACT = 'contract',
  TEMPORARY = 'temporary',
  INTERN = 'intern',
  FREELANCE = 'freelance',
}

/**
 * Enum định nghĩa tình trạng hôn nhân
 */
export enum MaritalStatus {
  SINGLE = 'single',
  MARRIED = 'married',
  DIVORCED = 'divorced',
  WIDOWED = 'widowed',
  SEPARATED = 'separated',
}

/**
 * Interface cho thông tin phòng ban trong nhân viên
 */
export interface EmployeeDepartmentDto {
  id: number;
  name: string;
  description: string | null;
  managerId: number | null;
  parentId: number | null;
}

/**
 * Interface cho dữ liệu nhân viên
 */
export interface EmployeeDto {
  id: number;
  userId: number;
  employeeCode: string;
  employeeName: string;
  departmentId: number | null;
  department?: EmployeeDepartmentDto | null;
  jobTitle: string | null;
  jobLevel: string | null;
  managerId: number | null;
  employmentType: EmploymentType | null;
  status: EmployeeStatus;
  hireDate: string | null;
  terminationDate: string | null;
  dateOfBirth: string | null;
  gender: string | null;
  maritalStatus: MaritalStatus | null;
  numberOfDependents: number | null;
  emergencyContactName: string | null;
  emergencyContactPhone: string | null;
  emergencyContactRelationship: string | null;
  notes: string | null;
  createdAt: number | null;
  updatedAt: number | null;
  createdBy: number | null;
}

/**
 * Interface cho tham số truy vấn danh sách nhân viên
 */
export interface EmployeeQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
  departmentId?: number;
  managerId?: number;
  status?: EmployeeStatus;
  employmentType?: EmploymentType;
}

/**
 * Interface cho dữ liệu tạo nhân viên mới (không cần userId)
 */
export interface CreateEmployeeDto {
  employeeName: string;
  departmentId?: number;
  jobTitle?: string;
  jobLevel?: string;
  managerId?: number;
  employmentType?: EmploymentType;
  status?: EmployeeStatus;
  hireDate?: string;
  dateOfBirth?: string;
  gender?: string;
  maritalStatus?: MaritalStatus;
  numberOfDependents?: number;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  notes?: string;
}

/**
 * Interface cho thông tin người dùng khi tạo nhân viên
 */
export interface UserInfoDto {
  username: string;
  password: string;
  email: string;
  fullName: string;
}

/**
 * Interface cho dữ liệu tạo nhân viên kèm tài khoản người dùng
 */
export interface CreateEmployeeWithUserDto {
  userInfo: UserInfoDto;
  employeeName: string;
  departmentId?: number;
  jobTitle?: string;
  jobLevel?: string;
  managerId?: number;
  employmentType?: EmploymentType;
  status?: EmployeeStatus;
  hireDate?: string;
  dateOfBirth?: string;
  gender?: string;
  maritalStatus?: MaritalStatus;
  numberOfDependents?: number;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  notes?: string;
}

/**
 * Interface cho dữ liệu cập nhật nhân viên
 */
export interface UpdateEmployeeDto {
  employeeCode?: string;
  employeeName?: string;
  departmentId?: number;
  jobTitle?: string;
  jobLevel?: string;
  managerId?: number;
  employmentType?: EmploymentType;
  status?: EmployeeStatus;
  hireDate?: string;
  terminationDate?: string;
  dateOfBirth?: string;
  gender?: string;
  maritalStatus?: MaritalStatus;
  numberOfDependents?: number;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  notes?: string;
}

/**
 * Interface cho dữ liệu phân quyền nhân viên
 */
export interface AssignRoleDto {
  roleIds: number[];
}

/**
 * Interface cho dữ liệu tạo tài khoản người dùng cho nhân viên
 */
export interface CreateUserForEmployeeDto {
  /**
   * ID của nhân viên
   */
  employeeId: number;

  /**
   * Địa chỉ email
   */
  email: string;

  /**
   * Có tự động tạo mật khẩu không
   */
  autoGeneratePassword?: boolean;

  /**
   * Tên đăng nhập (chỉ cần khi không auto generate)
   */
  username?: string;

  /**
   * Mật khẩu (chỉ cần khi không auto generate)
   */
  password?: string;

  /**
   * Họ và tên đầy đủ (chỉ cần khi không auto generate)
   */
  fullName?: string;

  /**
   * ID phòng ban của người dùng
   */
  departmentId?: number;
}

/**
 * Interface cho phản hồi thông tin người dùng
 */
export interface UserResponseDto {
  id: number;
  username: string;
  email: string;
  fullName: string;
}

/**
 * Interface cho phản hồi thông tin nhân viên khi tạo kèm user
 */
export interface EmployeeWithUserResponseDto {
  user: UserResponseDto;
  employee: EmployeeDto;
}
